import { create } from "zustand";
import { persist } from "zustand/middleware";

/**
 * 全局文件夹状态管理
 * 用于在不同页面之间保持文件夹选中状态
 */
export const useFolderStore = create(
  persist(
    (set, get) => ({
      // 当前选中的文件夹ID
      selectedFolderId: "all",
      
      // 文件夹列表
      folders: [],
      
      // 设置选中的文件夹
      setSelectedFolderId: (folderId) => {
        set({ selectedFolderId: folderId });
      },
      
      // 设置文件夹列表
      setFolders: (folders) => {
        set({ folders });
      },
      
      // 添加新文件夹
      addFolder: (folder) => {
        set((state) => ({
          folders: [...state.folders, folder],
          selectedFolderId: folder.id, // 自动选中新创建的文件夹
        }));
      },
      
      // 更新文件夹
      updateFolder: (folderId, updates) => {
        set((state) => ({
          folders: state.folders.map((folder) =>
            folder.id === folderId ? { ...folder, ...updates } : folder
          ),
        }));
      },
      
      // 删除文件夹
      removeFolder: (folderId) => {
        set((state) => ({
          folders: state.folders.filter((folder) => folder.id !== folderId),
          // 如果删除的是当前选中的文件夹，切换到"all"
          selectedFolderId: state.selectedFolderId === folderId ? "all" : state.selectedFolderId,
        }));
      },
      
      // 根据ID获取文件夹
      getFolderById: (folderId) => {
        const state = get();
        return state.folders.find((folder) => folder.id === folderId);
      },
      
      // 重置状态（用于登出等场景）
      reset: () => {
        set({
          selectedFolderId: "all",
          folders: [],
        });
      },
    }),
    {
      name: "folder-store", // localStorage key
      // 只持久化选中的文件夹ID，文件夹列表每次重新获取
      partialize: (state) => ({ selectedFolderId: state.selectedFolderId }),
    }
  )
);
