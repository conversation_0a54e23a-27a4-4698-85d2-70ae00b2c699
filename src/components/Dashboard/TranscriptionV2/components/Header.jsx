"use client";

import { useState } from "react";
import { Arrow<PERSON><PERSON><PERSON>, Share2, Clock, YoutubeIcon, Info } from "lucide-react";
import { ServiceProviderDebug } from "./ServiceProviderDebug";
import { But<PERSON> } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from "@/components/ui/tooltip";
import { Link } from "@/components/Common/Link";
import ExportFileMenu from "../../ExportFileMenu";
import GuestModeDialog from "../../GuestModeDialog";
import { useRouter } from "@/i18n/navigation";
import { useTranslations } from "next-intl";

export function Header({
  title,
  fileId,
  isYouTube,
  planConfig,
  duration,
  onShare,
  transcriptionType,
  insufficientMinutes,
  isAnonymous,
  transcriptionTime,
  mediaDownloadTime,
  mediaProcessingTime,
  sourceUrl,
  taskStatuses,
  requestedServiceProvider,
  actualServiceProvider,
}) {
  const t = useTranslations("transcription");
  const router = useRouter();
  const [showGuestModeDialog, setShowGuestModeDialog] = useState(false);
  const [guestDialogSource, setGuestDialogSource] = useState(
    "back_to_home_dialog"
  );

  // 使用后端返回的转录时间和媒体处理时间，如果没有则默认为0
  const transcriptionTimeSeconds = transcriptionTime || 0;
  // 优先使用 mediaProcessingTime，如果没有则使用 mediaDownloadTime
  const mediaPreprocessingTimeSeconds =
    mediaProcessingTime || mediaDownloadTime || 0;

  // 格式化转录时间显示
  const formatTranscriptionTime = (seconds) => {
    if (seconds < 60) {
      return t("transcriptionTime.seconds", { seconds });
    } else {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return remainingSeconds > 0
        ? t("transcriptionTime.minutesAndSeconds", {
            minutes,
            seconds: remainingSeconds,
          })
        : t("transcriptionTime.minutes", { minutes });
    }
  };

  const handleShare = (e) => {
    if (isAnonymous) {
      e.preventDefault();
      setGuestDialogSource("transcript_share");
      setShowGuestModeDialog(true);
      return;
    }
    onShare?.();
  };

  const handleBack = () => {
    if (isAnonymous) {
      // 匿名用户直接显示弹框提示
      setGuestDialogSource("back_to_home_dialog");
      setShowGuestModeDialog(true);
    } else {
      // 使用浏览器的返回功能，自然保持导航历史
      if (window.history.length > 1) {
        window.history.back();
      } else {
        // 如果没有历史记录，则跳转到dashboard
        router.push("/dashboard");
      }
    }
  };

  const handleGuestModeDialogClose = () => {
    setShowGuestModeDialog(false);
  };

  return (
    <div className="flex items-center justify-between px-6 h-16">
      {/* 左侧：返回按钮和标题 */}
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="icon"
          onClick={handleBack}
          className="hover:bg-gray-100"
        >
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <div className="flex flex-col">
          <h1 className="text-lg font-medium truncate max-w-[150px] sm:max-w-[400px] md:max-w-[600px]">
            {title}
          </h1>
          {/* 添加转录完成时间标签和服务提供商信息 - 采用左右布局 */}
          <div className="flex items-center w-full">
            {/* 左侧：转录时间 */}
            {transcriptionTimeSeconds > 0 && (
              <div className="flex items-center">
                <Clock className="h-3.5 w-3.5 text-gray-500 mr-1" />
                {mediaPreprocessingTimeSeconds > 0 ? (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <span className="text-xs text-gray-500 flex items-center">
                          {t("processingTime.label", {
                            defaultValue: "Processing Time",
                          })}
                          :{" "}
                          {formatTranscriptionTime(
                            transcriptionTimeSeconds +
                              mediaPreprocessingTimeSeconds
                          )}
                          <Info className="h-3.5 w-3.5 ml-1 text-gray-400" />
                        </span>
                      </TooltipTrigger>
                      <TooltipContent>
                        <div className="space-y-1">
                          <p className="text-xs">
                            {t("mediaPreprocessingTime.label", {
                              defaultValue: "Media preprocessing time",
                            })}
                            :{" "}
                            {formatTranscriptionTime(
                              mediaPreprocessingTimeSeconds
                            )}
                          </p>
                          <p className="text-xs">
                            {t("transcriptionTime.label")}:{" "}
                            {formatTranscriptionTime(transcriptionTimeSeconds)}
                          </p>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                ) : (
                  <span className="text-xs text-gray-500">
                    {t("transcriptionTime.label")}:{" "}
                    {formatTranscriptionTime(transcriptionTimeSeconds)}
                  </span>
                )}
              </div>
            )}

            {/* 右侧：服务提供商调试信息 */}
            <div className="ml-auto pl-12">
              <ServiceProviderDebug
                requestedProvider={requestedServiceProvider}
                actualProvider={actualServiceProvider}
              />
            </div>
          </div>
        </div>
      </div>

      {/* 右侧：操作按钮 */}
      <div className="flex items-center gap-2">
        {isYouTube && sourceUrl && (
          <Link href={sourceUrl} target="_blank" rel="noopener noreferrer">
            <Button
              variant="outline"
              size="icon"
              className="text-red-500 hover:text-red-600 hover:bg-red-50"
            >
              <YoutubeIcon className="h-5 w-5" />
            </Button>
          </Link>
        )}
        <Button
          variant="outline"
          size="icon"
          onClick={handleShare}
          className="text-custom-bg hover:text-custom-bg-600 hover:bg-custom-bg-100"
        >
          <Share2 className="h-5 w-5" />
        </Button>
        <ExportFileMenu
          fileId={fileId}
          planConfig={planConfig}
          duration={duration}
          isSharedPage={false}
          transcriptionType={transcriptionType}
          isAnonymous={isAnonymous}
          insufficientMinutes={insufficientMinutes}
          taskStatuses={taskStatuses}
          exportSource="transcription_detail"
        />
      </div>

      {/* 匿名用户访客弹框 */}
      <GuestModeDialog
        isOpen={showGuestModeDialog}
        onClose={handleGuestModeDialogClose}
        source={guestDialogSource}
      />
    </div>
  );
}
