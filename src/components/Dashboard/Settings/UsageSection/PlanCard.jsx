/**
 * Shared PlanCard component used in both Dashboard and Settings
 */

import { useTranslations } from "next-intl";
import { ArrowR<PERSON>, Timer } from "lucide-react";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import {
  PLAN_TYPES,
  PLAN_COLORS,
  getPlanTypeLabel,
  getActionButtonText,
} from "@/lib/planUtils";

// Settings version - more detailed layout
export function SettingsPlanCard({ plan, onManagePlan, disabled = false }) {
  const t = useTranslations("dashboard.remainingMinutes");

  // Get badge classes based on plan type
  const getBadgeClass = (type) => {
    const colorSet = PLAN_COLORS[type] || PLAN_COLORS[PLAN_TYPES.FREE];
    return cn(
      "text-[10px] px-1.5 py-0.5 leading-none min-w-[50px] text-center inline-block text-white rounded-md whitespace-nowrap",
      colorSet.badge,
      colorSet.badgeHover
    );
  };

  // Get progress bar class based on plan type
  const getProgressBarClass = (type) => {
    const colorSet = PLAN_COLORS[type] || PLAN_COLORS[PLAN_TYPES.FREE];
    return cn(
      "h-full rounded-full transition-all duration-500 ease-in-out",
      colorSet.progressBar
    );
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString || isNaN(new Date(dateString).getTime())) return null;
    const date = new Date(dateString);
    return format(date, "MMM dd, yyyy");
  };

  const usagePercentage = Math.min(
    ((plan.totalMinutes - plan.remainingMinutes) / plan.totalMinutes) * 100,
    100
  );

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 hover:bg-gray-50/50 transition-colors">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <h4 className="text-sm font-medium text-gray-900">{plan.name}</h4>
          <Badge className={getBadgeClass(plan.type)}>
            {getPlanTypeLabel(plan.type)}
          </Badge>
        </div>
        <div className="text-right">
          <div className="text-sm font-semibold text-custom-bg">
            {plan.remainingMinutes} {t("minutesLabel")}
          </div>
          <div className="text-xs text-gray-500">
            {t("total")}: {plan.totalMinutes}
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mb-3">
        <div className="h-2 w-full bg-gray-100 rounded-full overflow-hidden">
          <div
            className={getProgressBarClass(plan.type)}
            style={{ width: `${usagePercentage}%` }}
          />
        </div>
      </div>

      {/* Date and Action */}
      <div className="flex items-center justify-between">
        <div className="text-xs text-gray-500">
          {plan.resetsAt
            ? t("resetsOn", { date: formatDate(plan.resetsAt) })
            : plan.expiresAt
            ? t("expiresOn", { date: formatDate(plan.expiresAt) })
            : ""}
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onManagePlan(plan.type)}
          disabled={disabled}
          className="text-xs h-7 px-2 text-custom-bg hover:text-custom-bg-600 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {getActionButtonText(plan.type, t)}
          <ArrowRight className="ml-1 h-3 w-3" />
        </Button>
      </div>
    </div>
  );
}
