import {
  <PERSON><PERSON><PERSON><PERSON>,
  Loader,
  CircleX,
  LoaderCircle,
  Hourglass,
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from "@/components/ui/tooltip";
import { FILE_STATUS } from "@/constants/file";
import { useTranslations } from "next-intl";

export const StatusIcon = ({ status }) => {
  const t = useTranslations("common.statusIcon");
  const iconProps = "w-4 h-4";

  switch (status) {
    case FILE_STATUS.UPLOADING:
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <span>
                <Hourglass className={`text-custom-bg-600 ${iconProps}`} />
              </span>
            </TooltipTrigger>
            <TooltipContent>
              <p>{t("uploading")}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    case FILE_STATUS.UPLOADED:
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <span>
                <Hourglass className={`text-custom-bg-600 ${iconProps}`} />
              </span>
            </TooltipTrigger>
            <TooltipContent>
              <p>{t("uploaded")}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    case FILE_STATUS.PREPROCESSING:
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <span>
                <Hourglass className={`text-custom-bg-600 ${iconProps}`} />
              </span>
            </TooltipTrigger>
            <TooltipContent>
              <p>{t("preprocessing")}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    case FILE_STATUS.PREPROCESSING_FAILED:
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <span>
                <CircleX className={`text-red-600 ${iconProps}`} />
              </span>
            </TooltipTrigger>
            <TooltipContent>
              <p>{t("preprocessingFailed")}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    case FILE_STATUS.PROCESSING:
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <span>
                <Loader className={`text-custom-bg-600 ${iconProps}`} />
              </span>
            </TooltipTrigger>
            <TooltipContent>
              <p>{t("processing")}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    case FILE_STATUS.PARTIALLY_COMPLETED:
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <span>
                <LoaderCircle className={`text-custom-bg-600 ${iconProps}`} />
              </span>
            </TooltipTrigger>
            <TooltipContent>
              <p>{t("partiallyCompleted")}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    case FILE_STATUS.COMPLETED_WITH_ERRORS:
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <span>
                <LoaderCircle className={`text-yellow-600 ${iconProps}`} />
              </span>
            </TooltipTrigger>
            <TooltipContent>
              <p>{t("completedWithErrors")}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    case FILE_STATUS.COMPLETED:
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <span>
                <CircleCheck className={`text-custom-bg-600 ${iconProps}`} />
              </span>
            </TooltipTrigger>
            <TooltipContent>
              <p>{t("completed")}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    case FILE_STATUS.FAILED:
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <span>
                <CircleX className={`text-red-600 ${iconProps}`} />
              </span>
            </TooltipTrigger>
            <TooltipContent>
              <p>{t("failed")}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    default:
      return null;
  }
};
