// upload
export const GENERATE_SIGNED_URL = "/upload/generate-signed-url";
export const COMPLETE_UPLOAD = "/upload/complete";

// transcription
export const GET_TRANSCRIPTIONS = "/transcriptions";
export const GET_TRANSCRIPTIONS_BY_PAGE = "/transcriptions/page";
export const GET_TRANSCRIPTION_BY_ID = (id) => `/transcriptions/${id}`;
export const RENAME_TRANSCRIPTION = (id) => `/transcriptions/${id}`;
export const UPDATE_LANGUAGE_CODE = (id) => `/transcriptions/${id}`;
export const DELETE_TRANSCRIPTION = (id) => `/transcriptions/${id}`;
export const DELETE_ORIGINAL_FILE = (id) =>
  `/transcriptions/${id}/delete-original-file`;
export const BATCH_DELETE_TRANSCRIPTIONS = "/transcriptions/batch-delete";
export const BATCH_MOVE_TRANSCRIPTIONS = "/transcriptions/batch-folder";
export const UPDATE_TRANSCRIPTION_TYPE = (id) => `/transcriptions/${id}`;
export const UPDATE_SPEAKER_RECOGNITION = (id) => `/transcriptions/${id}`;
export const UPDATE_SEGMENT = (fileId, segmentId) =>
  `/transcriptions/${fileId}/segments/${segmentId}`;
export const BATCH_UPDATE_SEGMENTS = (fileId) =>
  `/transcriptions/${fileId}/segments/batch`;
export const BATCH_UPDATE_SPEAKERS = (fileId) =>
  `/transcriptions/${fileId}/speakers/batch`;
export const SEARCH_TRANSCRIPTIONS = "/transcriptions/search";
export const UNLOCK_TRANSCRIPTION = (id) => `/transcriptions/${id}/unlock`;
export const MOVE_TRANSCRIPTION_TO_FOLDER = (id) =>
  `/transcriptions/${id}/folder`;

export const EXPORT_TRANSCRIPTION = "/export";
export const BATCH_EXPORT_TRANSCRIPTIONS = "/export/batch";
export const EXPORT_OUTLINE = "/export/outline";
export const CREATE_TRANSCRIPTION_TASK = "/tasks/transcription";
export const RETRY_TASK = "/tasks/retry";
export const GET_TASK_STATUS = (taskId) => `/tasks/${taskId}/status`;

export const CREATE_YOUTUBE_TRANSCRIPTION_TASK = "/tasks/youtube-transcription";

// subscription
export const CREATE_CHECKOUT_SESSION = "/checkout/stripe";
export const CREATE_CUSTOMER_PORTAL = "/customer-portal";
export const REACTIVATE_SUBSCRIPTION = "/subscription/reactivate";

// user
export const GET_USER = "/user";
export const UPDATE_USER = "/user";
export const DEACTIVATE_USER = "/user/deactivate";
export const GET_USAGE = "/usage"; // deprecated, use GET_ENTITLEMENTS instead
export const GET_ENTITLEMENTS = "/entitlements";
export const GET_USER_LIMITS = "/user/limits";

// plan
export const PLAN_CONFIG = "/plan-config";

// share
export const CREATE_SHARE = (id) => `/transcriptions/${id}/share`;
export const GET_SHARE_BY_CODE = (code) => `/shares/${code}`;
export const EXPORT_SHARE_TRANSCRIPTION = "/shares/export";
export const SHARER_PLAN_CONFIG = "/sharer/plan-config";

// tools
export const GET_YOUTUBE_INFO = "/tools/youtube-info";
export const GET_YOUTUBE_SUBTITLES = "/tools/youtube-subtitle-list";
export const DOWNLOAD_YOUTUBE_SUBTITLE = "/tools/youtube-subtitle-download";

// email
export const CHECK_EMAIL_STATUS = "/user/email-check";

// anonymous
export const GET_ANONYMOUS_LATEST_FILE = "/transcriptions/anonymous/latest";
export const MIGRATE_ANONYMOUS_FILE = "/transcriptions/anonymous/migrate";

// appsumo
export const APPSUMO_ACTIVATE = "/auth/appsumo/activate";

// folders
export const GET_FOLDERS = "/folders";
export const CREATE_FOLDER = "/folders";
export const UPDATE_FOLDER = (id) => `/folders/${id}`;
export const DELETE_FOLDER = (id) => `/folders/${id}`;
export const GET_FOLDER_TRANSCRIPTIONS = (id) =>
  `/folders/${id}/transcriptions`;

// api keys
export const GET_API_KEYS = "/api-keys";
export const CREATE_API_KEY = "/api-keys";
export const UPDATE_API_KEY = (id) => `/api-keys/${id}`;
export const RESET_API_KEY = (id) => `/api-keys/${id}/reset`;
export const DELETE_API_KEY = (id) => `/api-keys/${id}`;
