import {
  CREATE_CHECKOUT_SESSION,
  CREATE_CUSTOMER_PORTAL,
  PLAN_CONFIG,
  REACTIVATE_SUBSCRIPTION,
} from "@/constants/endpoints";
import axiosInstance from "./axiosInstance";

export const subscriptionService = {
  createCheckoutSession: async (
    plan,
    promotionCode = null,
    mode = "subscription",
    source = "unknown"
  ) => {
    const response = await axiosInstance.post(CREATE_CHECKOUT_SESSION, {
      plan: plan,
      quantity: 1,
      mode: mode,
      promotionCode: promotionCode,
      source: source,
    });
    return response;
  },

  createCustomerPortal: async () => {
    const response = await axiosInstance.post(CREATE_CUSTOMER_PORTAL);
    return response;
  },

  reactivateSubscription: async () => {
    const response = await axiosInstance.post(REACTIVATE_SUBSCRIPTION);
    return response;
  },

  getPlanConfig: async () => {
    const response = await axiosInstance.get(PLAN_CONFIG);
    return response;
  },
};
