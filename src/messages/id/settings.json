{"page": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON> pengaturan dan preferensi akun <PERSON>", "backButton": "Kembali"}, "navigation": {"profile": "Profil", "preferences": "<PERSON><PERSON><PERSON><PERSON>", "notifications": "Notif<PERSON><PERSON>", "dangerZone": "Zona Bahaya", "apiKeyManagement": "Kunci API", "usage": "<PERSON><PERSON><PERSON><PERSON>"}, "profile": {"title": "Profil", "description": "Kelola informasi pribadi Anda", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "emailAddress": "<PERSON><PERSON><PERSON>", "save": "Simpan", "saving": "Menyimpan...", "saved": "Disimpan!", "successMessage": "<PERSON><PERSON> be<PERSON><PERSON><PERSON> disimpan", "errorMessage": "Gagal menyimpan profil. Silakan coba lagi."}, "preferences": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>la preferensi aplikasi Anda", "language": {"label": "Bahasa Antarmuka", "description": "<PERSON><PERSON><PERSON> bahasa yang Anda inginkan untuk antarmuka aplikasi"}, "timezone": {"label": "Zona Waktu", "description": "<PERSON><PERSON>h zona waktu Anda", "placeholder": "<PERSON><PERSON>h zona waktu...", "searchPlaceholder": "Cari zona waktu...", "loading": "Memuat zona waktu...", "successMessage": "Zona waktu ber<PERSON><PERSON> diper<PERSON>ui", "errorMessage": "Gagal memperbarui zona waktu. Silakan coba lagi."}}, "notifications": {"title": "Notifikasi <PERSON>", "description": "Kelola preferensi notifikasi email Anda", "transcriptionSuccess": {"label": "Notifikasi Keberhasilan Transkripsi", "description": "Terima pemberitahuan ketika transkripsi audio Anda selesai"}, "quotaReset": {"label": "Pemberitahuan Reset Kuota Transkripsi", "description": "Terima notifikasi ketika kuota transkripsi Anda direset"}, "productUpdates": {"label": "Pembaruan Produk dan <PERSON>", "description": "Terima pemberitahuan tentang pembaruan produk dan fitur baru"}, "successMessage": "Pengaturan notifika<PERSON>er<PERSON>ui", "errorMessage": "<PERSON><PERSON> memperbarui pengaturan notifikasi. Silakan coba lagi."}, "dangerZone": {"title": "Zona Bahaya", "description": "Tindakan yang tidak dapat di<PERSON>an", "deleteAccount": {"label": "Hapus Akun", "description": "Ha<PERSON> akun Anda dan semua data secara permanen", "button": "Hapus Akun", "dialog": {"title": "Hapus Akun", "description": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus akun Anda?", "warning": "Tindakan ini tidak dapat dibatalkan.", "consequences": {"title": "Saat <PERSON>a menghapus akun <PERSON>:", "items": ["Semua data Anda akan dihapus secara permanen.", "<PERSON>a akan kehilangan akses ke semua transkripsi <PERSON>a.", "<PERSON><PERSON><PERSON> (jika berlaku)", "<PERSON>kun Anda tidak dapat dipulihkan", "<PERSON><PERSON>a tidak akan dapat membuat akun baru lagi."]}, "confirmation": {"label": "Untuk mengonfirmasi, ketik DELETE di bawah:", "placeholder": "Ketik DELETE di sini"}, "buttons": {"cancel": "Batalkan", "delete": "Hapus", "deleting": "Mengh<PERSON>us..."}, "successMessage": "<PERSON><PERSON><PERSON>a te<PERSON>", "errorMessage": "<PERSON><PERSON> menghapus akun. Silakan coba lagi."}}}, "apiKeyManagement": {"title": "Manajemen Kunci API", "description": "Kelola kunci API Anda untuk mengakses UniScribe API", "requiresSubscription": "Akses API memerlukan langganan aktif atau rencana LTD", "upgradePrompt": "Tingkatkan rencana Anda untuk mengakses fitur manajemen API", "createKey": "<PERSON><PERSON><PERSON> Kunci Bar<PERSON>", "createFirstKey": "Buat Kunci API Pertama Anda", "noKeys": "Tidak ada Kunci API", "noKeysDescription": "Anda belum membuat kunci API apapun.", "maxKeysReached": "Batas maksimum kunci API telah tercapai (5 kunci)", "maxKeysDescription": "Hapus kunci API yang ada sebelum membuat yang baru.", "keyName": "<PERSON><PERSON>", "keyNamePlaceholder": "Ma<PERSON>kkan nama untuk kunci API Anda", "expiration": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "noExpiration": "Tidak ada masa berlaku", "days": "hari", "createdAt": "Dibuat", "lastUsed": "<PERSON><PERSON><PERSON>", "neverUsed": "Tidak pernah digunakan", "actions": "<PERSON><PERSON><PERSON>", "rename": "Ganti nama", "reset": "<PERSON><PERSON>", "delete": "Hapus", "copy": "<PERSON><PERSON>", "copied": "Disalin!", "active": "Aktif", "expired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyPreview": "Prab preview", "fullKey": "Kunci API Penuh", "keyWarning": "Ini adalah satu-satunya waktu Anda akan melihat kunci API lengkap. Simpan dengan aman.", "keyStorageWarning": "<PERSON>ka Anda kehilangan kunci ini, Anda perlu meresetnya untuk mendapatkan yang baru.", "confirmDelete": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus kunci API ini?", "confirmDeleteDescription": "Tindakan ini tidak dapat dibatalkan. Semua aplikasi yang menggunakan kunci ini akan berhenti berfungsi segera.", "confirmReset": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin mengatur ulang kunci API ini?", "confirmResetDescription": "Ini akan mengh<PERSON>lkan nilai kunci baru. Kunci lama akan berhenti berfungsi segera.", "createDialog": {"title": "Buat Kunci API", "nameLabel": "<PERSON><PERSON>", "namePlaceholder": "misalnya, Kunci API Produksi", "expirationLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> (opsional)", "expirationPlaceholder": "<PERSON><PERSON><PERSON>", "expirationHelp": "Biarkan kosong untuk tanpa kedaluwarsa (1-3650 hari)", "cancel": "Batalkan", "create": "<PERSON><PERSON><PERSON>", "creating": "Membuat..."}, "renameDialog": {"title": "Ganti Nama Kunci API", "nameLabel": "<PERSON><PERSON>", "cancel": "Batalkan", "save": "Simpan", "saving": "Menyimpan...", "description": "Ubah nama kunci API Anda."}, "keyCreatedDialog": {"title": "API Key Berhasil Dibuat", "copyButton": "Salin Kunci API", "close": "<PERSON><PERSON><PERSON>"}, "keyResetDialog": {"title": "API Key Berhasil Direset", "copyButton": "Salin Kunci API Baru", "close": "<PERSON><PERSON><PERSON>"}, "successMessages": {"keyCreated": "Kunci API berhasil dibuat", "keyUpdated": "Kunci API berhasil diperbarui", "keyReset": "API key berhasil direset", "keyDeleted": "API key berhasil dihapus"}, "errorMessages": {"loadFailed": "Gagal memuat kunci API. Silakan coba lagi.", "createFailed": "Gagal membuat kunci API. Silakan coba lagi.", "updateFailed": "Gagal memperbarui kunci API. Silakan coba lagi.", "resetFailed": "Gagal untuk mereset kunci API. Silakan coba lagi.", "deleteFailed": "<PERSON>l menghapus kunci API. Silakan coba lagi.", "accessDenied": "Akses API memerlukan langganan aktif atau rencana LTD.", "maxKeysReached": "<PERSON><PERSON><PERSON> maksimum kunci API telah tercapai (5)", "invalidName": "<PERSON><PERSON> kunci API harus antara 1 dan 100 karakter", "invalidExpiration": "Kedaluwarsa harus antara 1 dan 3650 hari", "keyNotFound": "API key tidak ditemukan", "nameExists": "<PERSON>a kunci API sudah ada"}, "upgradePlan": "Rencana Upgrade", "viewApiDocs": "Lihat Dokumentasi API"}, "usage": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> penggunaan akun Anda dan kredit yang tersisa"}}