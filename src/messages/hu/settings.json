{"page": {"title": "Beállítások", "subtitle": "Kezelje fiókbeállításait és preferenciáit", "backButton": "<PERSON><PERSON><PERSON>"}, "navigation": {"profile": "Profil", "preferences": "Beállítások", "notifications": "Értesítések", "dangerZone": "Veszélyzóna", "apiKeyManagement": "API kulcsok", "usage": "<PERSON><PERSON><PERSON><PERSON>"}, "profile": {"title": "Profil", "description": "<PERSON><PERSON><PERSON> ad<PERSON>", "firstName": "Keresztnév", "lastName": "Vezetéknév", "emailAddress": "E-mail cím", "save": "Men<PERSON>s", "saving": "Mentés...", "saved": "Mentve!", "successMessage": "A profil sikeresen mentve", "errorMessage": "A profil mentése nem sikerült. K<PERSON>rj<PERSON>k, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>."}, "preferences": {"title": "Beállítások", "description": "Kezelje az alkalmazásbeállításait", "language": {"label": "Felhasználói felület nyelve", "description": "Válassza ki a kívánt nyelvet az alkalmazás felületéhez"}, "timezone": {"label": "Id<PERSON><PERSON><PERSON><PERSON>", "description": "Válassza ki az időzónáját", "placeholder": "Válassza ki az időzónát...", "searchPlaceholder": "Időzóna keresése...", "loading": "Időzónák betöltése...", "successMessage": "Időz<PERSON>a si<PERSON>esen frissítve", "errorMessage": "A időzóna frissítése nem sikerült. Kérjük, próbálja meg ú<PERSON>."}}, "notifications": {"title": "<PERSON><PERSON>", "description": "Kezelje az e-mail értesítési beállításait", "transcriptionSuccess": {"label": "<PERSON><PERSON><PERSON>", "description": "Értesítések fogadása, amikor az audio átirat elkészült"}, "quotaReset": {"label": "Átiratkvóta Visszaállítási Értesítések", "description": "Értesítések fogadása, amikor a transzkripciós kvóta visszaállításra kerül"}, "productUpdates": {"label": "Termékfrissítések és új funkciók", "description": "Értesítéseket kap a termékfrissítésekről és az új funkciókról"}, "successMessage": "Értesítési beállítások frissítve", "errorMessage": "A értesítési beállítások frissítése nem sikerült. Kérjük, próbá<PERSON>ja meg új<PERSON>."}, "dangerZone": {"title": "Veszélyzóna", "description": "Visszavonhatatlan műveletek", "deleteAccount": {"label": "Fiók törlése", "description": "A fiókjának és az összes adatának véglegesen törlése", "button": "Fiók törlése", "dialog": {"title": "Fiók törlése", "description": "<PERSON><PERSON><PERSON> ben<PERSON>, hogy tö<PERSON>ölni szeretné a fiókj<PERSON>t?", "warning": "Ez a mű<PERSON>et nem von<PERSON>ó v<PERSON>.", "consequences": {"title": "Amikor törli a fiókját:", "items": ["Minden adatát véglegesen törölni fogják.", "El fogja veszíteni a hozzáférést az összes átiratához.", "Az előfizetése lemondásra kerül (ha alkalmazható)", "A fiókja nem állítható v<PERSON>za.", "Az Ön e-mail címe már nem lesz képes új fiók létrehozására."]}, "confirmation": {"label": "A megerősítéshez írja be a DELETE szót alább:", "placeholder": "<PERSON><PERSON><PERSON> be a DELETE szót ide"}, "buttons": {"cancel": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Törlés", "deleting": "Törlés..."}, "successMessage": "A fiókja törölve lett.", "errorMessage": "A fiók törlése nem sikerült. Kérjük, prób<PERSON><PERSON><PERSON>."}}}, "apiKeyManagement": {"title": "API kulcs kezelése", "description": "Kezelje az API kulcsait az UniScribe API eléréséhez", "requiresSubscription": "Az API-hozzáférés aktív előfizetést vagy LTD tervet igényel.", "upgradePrompt": "Frissítse a tervét az API kezelési funkciók eléréséhez.", "createKey": "Új kulcs létrehozása", "createFirstKey": "Hozza létre az első API kulcsát", "noKeys": "Nincsenek API kulcsok", "noKeysDescription": "Még nem hozott létre API kulcsokat.", "maxKeysReached": "A maximális API kulcsok korlátja elérve (5 kulcs)", "maxKeysDescription": "Töröljön egy meglévő API kulcsot, mi<PERSON><PERSON><PERSON> újat hozna létre.", "keyName": "Kulcs neve", "keyNamePlaceholder": "Adjon meg egy nevet az API kulcsának", "expiration": "<PERSON><PERSON><PERSON><PERSON>", "noExpiration": "<PERSON><PERSON><PERSON>", "days": "napok", "createdAt": "Létrehozva", "lastUsed": "<PERSON><PERSON><PERSON><PERSON>", "neverUsed": "<PERSON><PERSON> nem has<PERSON>", "actions": "Műveletek", "rename": "Átnevezés", "reset": "Visszaállítás", "delete": "Törlés", "copy": "Másolat", "copied": "Másolva!", "active": "Aktív", "expired": "<PERSON><PERSON><PERSON><PERSON>", "keyPreview": "Kulcs előnézet", "fullKey": "Teljes API kulcs", "keyWarning": "Ez az egyetlen alkalom, amikor megtekintheti a teljes API kulcsot. Biztonságosan tárolja.", "keyStorageWarning": "Ha elveszíti ezt a kul<PERSON>ot, vissza kell állítania ahhoz, hogy <PERSON><PERSON> ka<PERSON>.", "confirmDelete": "<PERSON><PERSON><PERSON> ben<PERSON>, hogy törölni szeretné ezt az API kulcsot?", "confirmDeleteDescription": "<PERSON>z a mű<PERSON>et nem von<PERSON>ó v<PERSON>za. Minden alkalmazás, amely ezt a kulcsot has<PERSON>nálja, a<PERSON><PERSON> leáll.", "confirmReset": "<PERSON><PERSON><PERSON> ben<PERSON>, hogy vissza szeretné állítani ezt az API kulcsot?", "confirmResetDescription": "Ez egy új kulcsértéket fog generálni. A régi kulcs azonnal leáll.", "createDialog": {"title": "API kulcs létrehozása", "nameLabel": "Kulcs neve", "namePlaceholder": "pl. Termelési API kulcs", "expirationLabel": "Lej<PERSON><PERSON> (opcionális)", "expirationPlaceholder": "Napok száma", "expirationHelp": "Hagyja üresen a lejárat elhagyásához (1-3650 nap)", "cancel": "<PERSON><PERSON><PERSON><PERSON>", "create": "Kulcs létrehozása", "creating": "Létrehozás..."}, "renameDialog": {"title": "API kulcs átnevezése", "nameLabel": "<PERSON>j név", "cancel": "<PERSON><PERSON><PERSON><PERSON>", "save": "Men<PERSON>s", "saving": "Mentés...", "description": "Módosítsa az API kulcsának nevét."}, "keyCreatedDialog": {"title": "API kulcs sikeresen létrehozva", "copyButton": "API kulcs másolása", "close": "Bezárás"}, "keyResetDialog": {"title": "API kulcs sikeresen visszaállítva", "copyButton": "Másolás új API kulcs", "close": "Bezárás"}, "successMessages": {"keyCreated": "API kulcs sikeresen létrehozva", "keyUpdated": "Az API kulcs sikeresen frissítve.", "keyReset": "API kulcs sikeresen visszaállítva", "keyDeleted": "Az API kulcs sikeresen törölve."}, "errorMessages": {"loadFailed": "<PERSON><PERSON> betölteni az API kulcsokat. Kérjük, próbálja meg új<PERSON>.", "createFailed": "Az API kulcs létrehozása nem sikerült. Kérjük, próbálja meg újra.", "updateFailed": "Az API kulcs frissítése nem sikerült. Kérjük, prób<PERSON><PERSON><PERSON>.", "resetFailed": "Az API kulcs visszaállítása nem sikerült. Kérjük, próbá<PERSON>ja <PERSON>.", "deleteFailed": "Az API kulcs törlése nem sikerült. Kérjük, próbálja meg új<PERSON>.", "accessDenied": "Az API-hozzáférés aktív előfizetést vagy LTD tervet igényel.", "maxKeysReached": "Elérte a maximális API kulcsok száma (5)", "invalidName": "Az API kulcs neve 1 és 100 karakter között kell, hogy legyen.", "invalidExpiration": "A lejáratnak 1 és 3650 nap között kell lennie.", "keyNotFound": "API kulcs nem található", "nameExists": "Az API kulcs neve már lé<PERSON>zik"}, "upgradePlan": "Frissítési terv", "viewApiDocs": "API Dokumentáció Meg<PERSON>tése"}, "usage": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Tekintse meg fiókja használatát és a fennmaradó krediteket"}}