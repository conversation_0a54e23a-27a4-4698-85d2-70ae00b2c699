{"page": {"title": "Configurações", "subtitle": "<PERSON><PERSON><PERSON><PERSON> as configurações e preferências da sua conta", "backButton": "Voltar"}, "navigation": {"profile": "Perfil", "preferences": "Preferências", "notifications": "Notificações", "dangerZone": "Zona de Perigo", "apiKeyManagement": "<PERSON>ves de <PERSON>", "usage": "<PERSON><PERSON>"}, "profile": {"title": "Perfil", "description": "Gerencie suas informações pessoais", "firstName": "Primeiro Nome", "lastName": "Sobrenome", "emailAddress": "Endereço de E-mail", "save": "<PERSON><PERSON>", "saving": "Salvando...", "saved": "Salvo!", "successMessage": "Perfil salvo com sucesso", "errorMessage": "Falha ao salvar o perfil. Por favor, tente novamente."}, "preferences": {"title": "Preferências", "description": "<PERSON><PERSON><PERSON><PERSON> as preferências do seu aplicativo", "language": {"label": "Idioma da Interface", "description": "Escolha seu idioma preferido para a interface do aplicativo"}, "timezone": {"label": "<PERSON><PERSON>", "description": "Selecione seu fuso horário", "placeholder": "Selecionar fuso horário...", "searchPlaceholder": "Pesquisar fuso horário...", "loading": "Carregando fusos horários...", "successMessage": "<PERSON>so horário atualizado com sucesso", "errorMessage": "Falha ao atualizar o fuso horário. Por favor, tente novamente."}}, "notifications": {"title": "Notificações por Email", "description": "Gerencie suas preferências de notificação por e-mail", "transcriptionSuccess": {"label": "Notificações de Sucesso de Transcrição", "description": "Receba notificações quando sua transcrição de áudio estiver completa"}, "quotaReset": {"label": "Notificações de Redefinição de Cota de Transcrição", "description": "Receba notificações quando sua cota de transcrição for redefinida."}, "productUpdates": {"label": "Atualizações de Produto e Novos Recursos", "description": "Receba notificações sobre atualizações de produtos e novos recursos"}, "successMessage": "Configurações de notificação atualizadas", "errorMessage": "Falha ao atualizar as configurações de notificação. Por favor, tente novamente."}, "dangerZone": {"title": "Zona de Perigo", "description": "Ações que não podem ser desfeitas", "deleteAccount": {"label": "Excluir Conta", "description": "Excluir permanentemente sua conta e todos os dados", "button": "Excluir Conta", "dialog": {"title": "Excluir Conta", "description": "Você tem certeza de que deseja excluir sua conta?", "warning": "Esta ação não pode ser desfeita.", "consequences": {"title": "Quando você excluir sua conta:", "items": ["Todos os seus dados serão excluídos permanentemente.", "Você perderá o acesso a todas as suas transcrições.", "Sua assinatura será cancelada (se aplicável)", "Sua conta não pode ser recuperada", "Seu e-mail não poderá mais criar uma nova conta."]}, "confirmation": {"label": "Para confirmar, digite DELETE abaixo:", "placeholder": "Digite DELETE aqui"}, "buttons": {"cancel": "<PERSON><PERSON><PERSON>", "delete": "Excluir", "deleting": "Excluindo..."}, "successMessage": "Sua conta foi excluída.", "errorMessage": "Falha ao excluir a conta. Por favor, tente novamente."}}}, "apiKeyManagement": {"title": "Gerenciamento de Chave de API", "description": "Gerencie suas chaves de API para acessar a API UniScribe", "requiresSubscription": "O acesso à API requer uma assinatura ativa ou um plano LTD.", "upgradePrompt": "Atualize seu plano para acessar os recursos de gerenciamento de API.", "createKey": "Criar Nova Chave", "createFirstKey": "Crie sua Primeira Chave de API", "noKeys": "Sem Chaves de API", "noKeysDescription": "Você ainda não criou nenhuma chave de API.", "maxKeysReached": "Limite máximo de chaves de API atingido (5 chaves)", "maxKeysDescription": "Exclua uma chave de API existente antes de criar uma nova.", "keyName": "<PERSON>me da Chave", "keyNamePlaceholder": "Insira um nome para sua chave de API", "expiration": "Expiração", "noExpiration": "Sem expiração", "days": "dias", "createdAt": "<PERSON><PERSON><PERSON>", "lastUsed": "<PERSON><PERSON><PERSON>", "neverUsed": "Nunca utilizado", "actions": "Ações", "rename": "Renomear", "reset": "Redefinir", "delete": "Excluir", "copy": "Copiar", "copied": "Copiado!", "active": "Ativo", "expired": "<PERSON><PERSON><PERSON>", "keyPreview": "<PERSON><PERSON><PERSON>", "fullKey": "Chave API Completa", "keyWarning": "Esta é a única vez que você verá a chave de API completa. Armazene-a com segurança.", "keyStorageWarning": "Se você perder esta chave, precisará redefini-la para obter uma nova.", "confirmDelete": "Você tem certeza de que deseja excluir esta chave de API?", "confirmDeleteDescription": "Esta ação não pode ser desfeita. Todos os aplicativos que utilizam esta chave pararão de funcionar imediatamente.", "confirmReset": "Você tem certeza de que deseja redefinir esta chave de API?", "confirmResetDescription": "<PERSON><PERSON> gerará um novo valor de chave. A chave antiga deixará de funcionar imediatamente.", "createDialog": {"title": "<PERSON><PERSON><PERSON><PERSON>", "nameLabel": "<PERSON>me da Chave", "namePlaceholder": "por exemplo, Chave da API de Produção", "expirationLabel": "Expiração (opcional)", "expirationPlaceholder": "Número de <PERSON>as", "expirationHelp": "Deixe em branco para nenhuma expiração (1-3650 dias)", "cancel": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "creating": "Criando..."}, "renameDialog": {"title": "Renomear <PERSON><PERSON>", "nameLabel": "Novo Nome", "cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>", "saving": "Salvando...", "description": "Altere o nome da sua chave de API."}, "keyCreatedDialog": {"title": "Chave da API criada com sucesso", "copyButton": "Copiar chave da API", "close": "<PERSON><PERSON><PERSON>"}, "keyResetDialog": {"title": "Chave da API redefinida com sucesso", "copyButton": "Copiar Nova Chave da API", "close": "<PERSON><PERSON><PERSON>"}, "successMessages": {"keyCreated": "Chave de API criada com sucesso", "keyUpdated": "Chave da API atualizada com sucesso", "keyReset": "Chave da API redefinida com sucesso", "keyDeleted": "Chave da API excluída com sucesso"}, "errorMessages": {"loadFailed": "<PERSON><PERSON><PERSON> ao carregar as chaves da API. Por favor, tente novamente.", "createFailed": "Falha ao criar a chave da API. Por favor, tente novamente.", "updateFailed": "Falha ao atualizar a chave da API. Por favor, tente novamente.", "resetFailed": "Falha ao redefinir a chave da API. Por favor, tente novamente.", "deleteFailed": "Falha ao excluir a chave da API. Por favor, tente novamente.", "accessDenied": "O acesso à API requer uma assinatura ativa ou plano LTD.", "maxKeysReached": "Número máximo de chaves de API alcançado (5)", "invalidName": "O nome da chave da API deve ter entre 1 e 100 caracteres.", "invalidExpiration": "A expiração deve estar entre 1 e 3650 dias.", "keyNotFound": "Chave da API não encontrada", "nameExists": "O nome da chave da API já existe"}, "upgradePlan": "Plano de Upgrade", "viewApiDocs": "Ver Documentação da API"}, "usage": {"title": "<PERSON><PERSON>", "description": "Visualize o uso da sua conta e os créditos restantes"}}