{"page": {"title": "설정", "subtitle": "계정 설정 및 기본 설정을 관리하십시오.", "backButton": "뒤로"}, "navigation": {"profile": "프로필", "preferences": "환경 설정", "notifications": "알림", "dangerZone": "위험 구역", "apiKeyManagement": "API 키", "usage": "사용법"}, "profile": {"title": "프로필", "description": "개인 정보를 관리하십시오.", "firstName": "이름", "lastName": "성씨", "emailAddress": "이메일 주소", "save": "저장", "saving": "저장 중...", "saved": "저장되었습니다!", "successMessage": "프로필이 성공적으로 저장되었습니다.", "errorMessage": "프로필 저장에 실패했습니다. 다시 시도해 주십시오."}, "preferences": {"title": "환경 설정", "description": "애플리케이션 기본 설정 관리", "language": {"label": "인터페이스 언어", "description": "응용 프로그램 인터페이스에 대한 선호 언어를 선택하십시오."}, "timezone": {"label": "시간대", "description": "시간대를 선택하세요", "placeholder": "시간대 선택...", "searchPlaceholder": "시간대 검색...", "loading": "시간대 로딩 중...", "successMessage": "시간대가 성공적으로 업데이트되었습니다.", "errorMessage": "시간대 업데이트에 실패했습니다. 다시 시도해 주십시오."}}, "notifications": {"title": "이메일 알림", "description": "이메일 알림 설정을 관리하세요.", "transcriptionSuccess": {"label": "전사 성공 알림", "description": "오디오 전사가 완료되면 알림을 받습니다."}, "quotaReset": {"label": "전사 할당량 재설정 알림", "description": "전사 할당량이 초기화될 때 알림을 받습니다."}, "productUpdates": {"label": "제품 업데이트 및 새로운 기능", "description": "제품 업데이트 및 새로운 기능에 대한 알림을 받습니다."}, "successMessage": "알림 설정이 업데이트되었습니다.", "errorMessage": "알림 설정을 업데이트하지 못했습니다. 다시 시도해 주십시오."}, "dangerZone": {"title": "위험 구역", "description": "되돌릴 수 없는 작업", "deleteAccount": {"label": "계정 삭제", "description": "계정을 영구적으로 삭제하고 모든 데이터를 제거합니다.", "button": "계정 삭제", "dialog": {"title": "계정 삭제", "description": "정말로 계정을 삭제하시겠습니까?", "warning": "이 작업은 실행 취소할 수 없습니다.", "consequences": {"title": "계정을 삭제할 때:", "items": ["모든 데이터는 영구적으로 삭제됩니다.", "모든 전사 내용에 대한 접근 권한을 잃게 됩니다.", "귀하의 구독이 취소됩니다(해당되는 경우).", "귀하의 계정을 복구할 수 없습니다.", "귀하의 이메일로는 더 이상 새 계정을 생성할 수 없습니다."]}, "confirmation": {"label": "확인을 위해 아래에 DELETE를 입력하십시오:", "placeholder": "여기에 DELETE 입력하십시오"}, "buttons": {"cancel": "취소", "delete": "삭제", "deleting": "삭제 중..."}, "successMessage": "귀하의 계정이 삭제되었습니다.", "errorMessage": "계정을 삭제하지 못했습니다. 다시 시도해 주십시오."}}}, "apiKeyManagement": {"title": "API 키 관리", "description": "UniScribe API에 접근하기 위한 API 키를 관리하십시오.", "requiresSubscription": "API 액세스는 활성 구독 또는 LTD 플랜이 필요합니다.", "upgradePrompt": "API 관리 기능에 접근하려면 요금제를 업그레이드하세요.", "createKey": "새 키 생성", "createFirstKey": "첫 번째 API 키 생성하기", "noKeys": "API 키가 없습니다.", "noKeysDescription": "아직 API 키를 생성하지 않았습니다.", "maxKeysReached": "최대 API 키 제한에 도달했습니다 (5 키)", "maxKeysDescription": "새로운 API 키를 생성하기 전에 기존 API 키를 삭제하십시오.", "keyName": "키 이름", "keyNamePlaceholder": "API 키의 이름을 입력하세요.", "expiration": "만료", "noExpiration": "만료 없음", "days": "일수", "createdAt": "생성됨", "lastUsed": "마지막 사용됨", "neverUsed": "사용한 적 없음", "actions": "작업", "rename": "이름 바꾸기", "reset": "재설정", "delete": "삭제", "copy": "복사", "copied": "복사되었습니다!", "active": "활성화", "expired": "만료됨", "keyPreview": "키 미리보기", "fullKey": "전체 API 키", "keyWarning": "이것이 전체 API 키를 볼 수 있는 유일한 시간입니다. 안전하게 보관하십시오.", "keyStorageWarning": "이 키를 잃어버리면 새 키를 받기 위해 재설정해야 합니다.", "confirmDelete": "이 API 키를 삭제하시겠습니까?", "confirmDeleteDescription": "이 작업은 실행 취소할 수 없습니다. 이 키를 사용하는 모든 애플리케이션은 즉시 작동을 중지합니다.", "confirmReset": "이 API 키를 재설정하시겠습니까?", "confirmResetDescription": "이것은 새로운 키 값을 생성합니다. 이전 키는 즉시 작동을 중지합니다.", "createDialog": {"title": "API 키 생성", "nameLabel": "키 이름", "namePlaceholder": "예: Production API Key", "expirationLabel": "만료 (선택 사항)", "expirationPlaceholder": "일수", "expirationHelp": "만료 없음으로 두려면 비워 두십시오 (1-3650일)", "cancel": "취소", "create": "키 생성", "creating": "생성 중..."}, "renameDialog": {"title": "API 키 이름 변경", "nameLabel": "새 이름", "cancel": "취소", "save": "저장", "saving": "저장 중...", "description": "API 키의 이름을 변경하십시오."}, "keyCreatedDialog": {"title": "API 키가 성공적으로 생성되었습니다.", "copyButton": "API 키 복사", "close": "닫기"}, "keyResetDialog": {"title": "API 키가 성공적으로 재설정되었습니다.", "copyButton": "새 API 키 복사", "close": "닫기"}, "successMessages": {"keyCreated": "API 키가 성공적으로 생성되었습니다.", "keyUpdated": "API 키가 성공적으로 업데이트되었습니다.", "keyReset": "API 키가 성공적으로 재설정되었습니다.", "keyDeleted": "API 키가 성공적으로 삭제되었습니다."}, "errorMessages": {"loadFailed": "API 키를 로드하지 못했습니다. 다시 시도해 주십시오.", "createFailed": "API 키 생성에 실패했습니다. 다시 시도해 주십시오.", "updateFailed": "API 키 업데이트에 실패했습니다. 다시 시도해 주십시오.", "resetFailed": "API 키를 재설정하지 못했습니다. 다시 시도해 주십시오.", "deleteFailed": "API 키 삭제에 실패했습니다. 다시 시도해 주십시오.", "accessDenied": "API 액세스는 활성 구독 또는 LTD 플랜이 필요합니다.", "maxKeysReached": "최대 API 키 수에 도달했습니다 (5)", "invalidName": "API 키 이름은 1자에서 100자 사이여야 합니다.", "invalidExpiration": "만료일은 1일에서 3650일 사이여야 합니다.", "keyNotFound": "API 키를 찾을 수 없습니다.", "nameExists": "API 키 이름이 이미 존재합니다."}, "upgradePlan": "업그레이드 계획", "viewApiDocs": "API 문서 보기"}, "usage": {"title": "사용법", "description": "계정 사용량 및 남은 크레딧을 확인하세요."}}