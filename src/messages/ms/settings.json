{"page": {"title": "Tetapan", "subtitle": "<PERSON><PERSON> tetapan dan pilihan akaun anda", "backButton": "Kembali"}, "navigation": {"profile": "Profil", "preferences": "<PERSON><PERSON><PERSON><PERSON>", "notifications": "Pemberitahuan", "dangerZone": "Zon Bahaya", "apiKeyManagement": "Kunci API", "usage": "<PERSON><PERSON><PERSON><PERSON>"}, "profile": {"title": "Profil", "description": "<PERSON><PERSON> maklumat peribadi anda", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "emailAddress": "<PERSON><PERSON><PERSON>", "save": "Simpan", "saving": "Menyimpan...", "saved": "Disimpan!", "successMessage": "Profil disimpan dengan jayanya", "errorMessage": "Gagal menyimpan profil. Sila cuba lagi."}, "preferences": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> pilihan aplik<PERSON> anda", "language": {"label": "Bahasa Antara <PERSON>", "description": "<PERSON><PERSON><PERSON> bahasa pilihan anda untuk antara muka aplikasi"}, "timezone": {"label": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> zon waktu anda", "placeholder": "<PERSON><PERSON>h zon waktu...", "searchPlaceholder": "Cari zon waktu...", "loading": "Memuat zon waktu...", "successMessage": "Zon waktu telah dikemas kini dengan jayanya", "errorMessage": "Gagal untuk mengemas kini zon waktu. Sila cuba lagi."}}, "notifications": {"title": "Pemberitahuan E-mel", "description": "<PERSON><PERSON> pilihan pemberitahuan emel anda", "transcriptionSuccess": {"label": "Pemberitahuan Kejay<PERSON>kripsi", "description": "Terima notifikasi apabila transkripsi audio anda selesai"}, "quotaReset": {"label": "Pemberitahuan Reset Kuota Transkripsi", "description": "Terima notifikasi apabila kuota transkripsi anda diset semula"}, "productUpdates": {"label": "<PERSON><PERSON>du<PERSON> dan <PERSON>", "description": "Terima pemberitahuan mengenai kemas kini produk dan ciri-ciri baru"}, "successMessage": "Tetapan pemberitahuan telah dikemas kini", "errorMessage": "Gagal untuk mengemas kini tetapan pemberitahuan. Sila cuba lagi."}, "dangerZone": {"title": "Zon Bahaya", "description": "Tindakan yang tidak boleh di<PERSON>an", "deleteAccount": {"label": "<PERSON><PERSON>", "description": "<PERSON><PERSON> secara kekal akaun anda dan semua data", "button": "<PERSON><PERSON>", "dialog": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON>h anda pasti ingin memadamkan akaun anda?", "warning": "Tindakan ini tidak boleh di<PERSON>alkan.", "consequences": {"title": "<PERSON><PERSON><PERSON><PERSON> anda memada<PERSON>kan akaun anda:", "items": ["Semua data anda akan dipadamkan secara kekal.", "<PERSON>a akan kehilangan akses kepada semua transk<PERSON>si anda.", "<PERSON><PERSON><PERSON> anda akan <PERSON> (ji<PERSON> berk<PERSON>)", "<PERSON><PERSON><PERSON> anda tidak dapat dipulihkan", "<PERSON><PERSON> anda tidak lagi dapat membuat akaun baru."]}, "confirmation": {"label": "Untuk <PERSON>, taip DELETE di bawah:", "placeholder": "Taip DELETE di sini"}, "buttons": {"cancel": "<PERSON><PERSON>", "delete": "Padam", "deleting": "Mengh<PERSON>us..."}, "successMessage": "<PERSON><PERSON><PERSON> anda telah dipadam.", "errorMessage": "Gagal untuk memadamkan akaun. Sila cuba lagi."}}}, "apiKeyManagement": {"title": "Pengurusan Kunci API", "description": "Urus kunci API anda untuk mengakses API UniScribe", "requiresSubscription": "Akses API memerlukan langganan aktif atau pelan LTD", "upgradePrompt": "Tingkatkan pelan anda untuk mengakses ciri pengurusan API", "createKey": "<PERSON><PERSON><PERSON> Kunci Bar<PERSON>", "createFirstKey": "Buat Kunci API Pertama Anda", "noKeys": "Tiada Kunci API", "noKeysDescription": "Anda belum mencipta sebarang kunci API lagi.", "maxKeysReached": "<PERSON> maksimum kunci API telah dicapai (5 kunci)", "maxKeysDescription": "Padamkan kunci API yang sedia ada sebelum mencipta yang baru.", "keyName": "<PERSON><PERSON>", "keyNamePlaceholder": "Masukkan nama untuk kunci API anda", "expiration": "Penyelesaian", "noExpiration": "<PERSON><PERSON><PERSON> tarikh luput", "days": "hari", "createdAt": "<PERSON><PERSON><PERSON>", "lastUsed": "<PERSON><PERSON><PERSON>", "neverUsed": "Tidak pernah digunakan", "actions": "<PERSON><PERSON><PERSON>", "rename": "<PERSON><PERSON><PERSON> semula", "reset": "Tetapkan Semula", "delete": "Padam", "copy": "<PERSON><PERSON>", "copied": "Disalin!", "active": "Aktif", "expired": "<PERSON><PERSON> tempoh", "keyPreview": "<PERSON><PERSON><PERSON>", "fullKey": "Kunci API Penuh", "keyWarning": "Ini adalah satu-satunya masa anda akan melihat kunci API penuh. Simpan dengan selamat.", "keyStorageWarning": "<PERSON>ka anda kehilangan kunci ini, anda perlu menetapkannya semula untuk mendapatkan yang baru.", "confirmDelete": "<PERSON><PERSON>h anda pasti ingin memadam kunci API ini?", "confirmDeleteDescription": "Tindakan ini tidak boleh dibatalkan. Semua aplikasi yang menggunakan kunci ini akan berhenti berfungsi serta-merta.", "confirmReset": "<PERSON><PERSON>h anda pasti ingin menetapkan semula kunci API ini?", "confirmResetDescription": "Ini akan mengh<PERSON>lkan nilai kunci baru. Kunci lama akan berhenti berfungsi serta-merta.", "createDialog": {"title": "Buat Kunci API", "nameLabel": "<PERSON><PERSON>", "namePlaceholder": "<PERSON><PERSON><PERSON>, Kunci API Pengeluaran", "expirationLabel": "Pen<PERSON><PERSON>aia<PERSON> (pilihan)", "expirationPlaceholder": "Bilangan hari", "expirationHelp": "Biarkan kosong untuk tiada tamat tempoh (1-3650 hari)", "cancel": "<PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "creating": "Mencipta..."}, "renameDialog": {"title": "<PERSON><PERSON>", "nameLabel": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "save": "Simpan", "saving": "Menyimpan...", "description": "<PERSON>kar nama kunci API anda."}, "keyCreatedDialog": {"title": "Kunci API Dicipta Dengan Jayanya", "copyButton": "Salin Kunci API", "close": "<PERSON><PERSON><PERSON>"}, "keyResetDialog": {"title": "API Key Ditetapkan Semula Dengan Jayanya", "copyButton": "Salin Kunci API Baru", "close": "<PERSON><PERSON><PERSON>"}, "successMessages": {"keyCreated": "Kunci API telah berjaya dibuat", "keyUpdated": "Kunci API telah dikemas kini dengan jayanya", "keyReset": "Kunci API telah diset semula dengan jayanya", "keyDeleted": "Kunci API telah dipadamkan dengan jayanya."}, "errorMessages": {"loadFailed": "Gagal memuat kunci API. Sila cuba lagi.", "createFailed": "Gagal untuk mencipta kunci API. Sila cuba lagi.", "updateFailed": "Gagal untuk mengemas kini kunci API. Sila cuba lagi.", "resetFailed": "Gagal untuk menetapkan semula kunci API. Sila cuba lagi.", "deleteFailed": "Gagal untuk memadam kunci API. Sila cuba lagi.", "accessDenied": "Akses API memerlukan langganan aktif atau pelan LTD.", "maxKeysReached": "<PERSON><PERSON><PERSON> maksimum kunci API telah dicapai (5)", "invalidName": "<PERSON><PERSON> kunci API mesti antara 1 dan 100 aksara", "invalidExpiration": "Tempoh tamat mesti antara 1 hingga 3650 hari", "keyNotFound": "Kunci API tidak ditemui", "nameExists": "<PERSON>a kunci <PERSON> sudah wujud"}, "upgradePlan": "Pelan Peningkatan", "viewApiDocs": "Lihat Dokumentasi API"}, "usage": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> pengg<PERSON>an akaun anda dan kredit yang tinggal"}}