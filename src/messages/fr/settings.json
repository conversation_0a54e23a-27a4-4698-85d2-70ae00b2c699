{"page": {"title": "Paramètres", "subtitle": "<PERSON><PERSON><PERSON> les paramètres et préférences de votre compte", "backButton": "Retour"}, "navigation": {"profile": "Profil", "preferences": "Préférences", "notifications": "Notifications", "dangerZone": "Zone de danger", "apiKeyManagement": "Clés API", "usage": "Utilisation"}, "profile": {"title": "Profil", "description": "Gérez vos informations personnelles", "firstName": "Prénom", "lastName": "Nom de famille", "emailAddress": "Adresse e-mail", "save": "Enregistrer", "saving": "Enregistrement...", "saved": "Enregistré !", "successMessage": "Profil enregistré avec succès", "errorMessage": "Échec de l'enregistrement du profil. Veuillez réessayer."}, "preferences": {"title": "Préférences", "description": "<PERSON><PERSON><PERSON> les préférences de votre application", "language": {"label": "Langue de l'interface", "description": "Choisissez votre langue préférée pour l'interface de l'application."}, "timezone": {"label": "<PERSON><PERSON> ho<PERSON>", "description": "Sélectionnez votre fuseau horaire", "placeholder": "Sélectionner le fuseau horaire...", "searchPlaceholder": "Rechercher un fuseau horaire...", "loading": "Chargement des fuseaux horaires...", "successMessage": "<PERSON><PERSON> horaire mis à jour avec succès", "errorMessage": "Échec de la mise à jour du fuseau horaire. Veuillez réessayer."}}, "notifications": {"title": "Notifications par e-mail", "description": "<PERSON><PERSON><PERSON> vos préférences de notification par e-mail", "transcriptionSuccess": {"label": "Notifications de succès de transcription", "description": "Recevez des notifications lorsque votre transcription audio est terminée."}, "quotaReset": {"label": "Notifications de réinitialisation du quota de transcription", "description": "Recevez des notifications lorsque votre quota de transcription est réinitialisé."}, "productUpdates": {"label": "Mises à jour du produit et nouvelles fonctionnalités", "description": "Recevez des notifications concernant les mises à jour de produit et les nouvelles fonctionnalités."}, "successMessage": "Paramètres de notification mis à jour", "errorMessage": "Échec de la mise à jour des paramètres de notification. Veuillez réessayer."}, "dangerZone": {"title": "Zone de Danger", "description": "Actions qui ne peuvent pas être annulées", "deleteAccount": {"label": "Supprimer le compte", "description": "Supprimer définitivement votre compte et toutes les données", "button": "Supprimer le compte", "dialog": {"title": "Supprimer le compte", "description": "Êtes-vous sûr de vouloir supprimer votre compte ?", "warning": "Cette action ne peut pas être annulée.", "consequences": {"title": "Lors<PERSON> vous supprimez votre compte :", "items": ["Toutes vos données seront définitivement supprimées.", "Vous perdrez l'accès à toutes vos transcriptions.", "Votre abonnement sera annulé (le cas échéant)", "Votre compte ne peut pas être récupéré.", "Votre email ne pourra plus créer un nouveau compte."]}, "confirmation": {"label": "<PERSON>ur confirmer, tapez DELETE ci-dessous :", "placeholder": "Tapez DELETE ici"}, "buttons": {"cancel": "Annuler", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleting": "Suppression..."}, "successMessage": "Votre compte a été supprimé.", "errorMessage": "Échec de la suppression du compte. Veuillez réessayer."}}}, "apiKeyManagement": {"title": "Gestion des clés API", "description": "Gérez vos clés API pour accéder à l'API UniScribe", "requiresSubscription": "L'accès à l'API nécessite un abonnement actif ou un plan LTD.", "upgradePrompt": "Mettez à niveau votre plan pour accéder aux fonctionnalités de gestion de l'API.", "createKey": "<PERSON><PERSON>er une nouvelle clé", "createFirstKey": "<PERSON><PERSON>ez votre première clé API", "noKeys": "Pas de clés API", "noKeysDescription": "Vous n'avez pas encore créé de clés API.", "maxKeysReached": "Limite maximale de clés API atteinte (5 clés)", "maxKeysDescription": "Supprimez une clé API existante avant de créer une nouvelle.", "keyName": "Nom de la clé", "keyNamePlaceholder": "Entrez un nom pour votre clé API", "expiration": "Expiration", "noExpiration": "Aucune expiration", "days": "jours", "createdAt": "<PERSON><PERSON><PERSON>", "lastUsed": "Dernière utilisation", "neverUsed": "<PERSON><PERSON> utilis<PERSON>", "actions": "Actions", "rename": "<PERSON>mmer", "reset": "Réinitialiser", "delete": "<PERSON><PERSON><PERSON><PERSON>", "copy": "<PERSON><PERSON><PERSON>", "copied": "Copié !", "active": "Actif", "expired": "Expiré", "keyPreview": "Aperçu clé", "fullKey": "Clé API complète", "keyWarning": "C'est la seule fois où vous verrez la clé API complète. Conservez-la en toute sécurité.", "keyStorageWarning": "Si vous perdez cette clé, vous devrez la réinitialiser pour en obtenir une nouvelle.", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cette clé API ?", "confirmDeleteDescription": "Cette action ne peut pas être annulée. Toutes les applications utilisant cette clé cesseront de fonctionner immédiatement.", "confirmReset": "Êtes-vous sûr de vouloir réinitialiser cette clé API ?", "confirmResetDescription": "<PERSON><PERSON>rera une nouvelle valeur de clé. L'ancienne clé cessera de fonctionner immédiatement.", "createDialog": {"title": "<PERSON><PERSON><PERSON> une clé API", "nameLabel": "Nom de la clé", "namePlaceholder": "Clé API de production", "expirationLabel": "Expiration (optionnel)", "expirationPlaceholder": "Nombre de jours", "expirationHelp": "Leave empty for no expiration (1-3650 days)", "cancel": "Annuler", "create": "<PERSON><PERSON><PERSON> une clé", "creating": "Création en cours..."}, "renameDialog": {"title": "Renommer la clé API", "nameLabel": "Nouveau nom", "cancel": "Annuler", "save": "Enregistrer", "saving": "Enregistrement...", "description": "Changez le nom de votre clé API."}, "keyCreatedDialog": {"title": "Clé API créée avec succès", "copyButton": "Copier la clé API", "close": "<PERSON><PERSON><PERSON>"}, "keyResetDialog": {"title": "Réinitialisation de la clé API réussie", "copyButton": "Copier la nouvelle clé API", "close": "<PERSON><PERSON><PERSON>"}, "successMessages": {"keyCreated": "Clé API créée avec succès", "keyUpdated": "Clé API mise à jour avec succès", "keyReset": "La clé API a été réinitialisée avec succès.", "keyDeleted": "Clé API supprimée avec succès"}, "errorMessages": {"loadFailed": "Échec du chargement des clés API. Veuillez réessayer.", "createFailed": "Échec de la création de la clé API. Veuillez réessayer.", "updateFailed": "Échec de la mise à jour de la clé API. Veuillez réessayer.", "resetFailed": "Échec de la réinitialisation de la clé API. Veuillez réessayer.", "deleteFailed": "Échec de la suppression de la clé API. Veuillez réessayer.", "accessDenied": "L'accès à l'API nécessite un abonnement actif ou un plan LTD.", "maxKeysReached": "Nombre maximum de clés API atteint (5)", "invalidName": "Le nom de la clé API doit comporter entre 1 et 100 caractères.", "invalidExpiration": "La date d'expiration doit être comprise entre 1 et 3650 jours.", "keyNotFound": "Clé API non trouvée", "nameExists": "Le nom de la clé API existe déjà."}, "upgradePlan": "Plan de mise à niveau", "viewApiDocs": "Voir la documentation de l'API"}, "usage": {"title": "Utilisation", "description": "Consultez l'utilisation de votre compte et les crédits restants"}}