{"page": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Hesap ayarlarınızı ve tercihlerinizi yönetin", "backButton": "<PERSON><PERSON>"}, "navigation": {"profile": "Profil", "preferences": "<PERSON><PERSON><PERSON><PERSON>", "notifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dangerZone": "<PERSON><PERSON><PERSON>", "apiKeyManagement": "API Anahtarları", "usage": "Kullanım"}, "profile": {"title": "Profil", "description": "Kişisel bilgilerinizi yö<PERSON>in", "firstName": "İsim", "lastName": "Soyadı", "emailAddress": "E-posta Adresi", "save": "<PERSON><PERSON>", "saving": "Kay<PERSON>ili<PERSON>r...", "saved": "<PERSON><PERSON><PERSON><PERSON>!", "successMessage": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>.", "errorMessage": "Profil kaydedilemedi. Lütfen tekrar deneyin."}, "preferences": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Uygulama terc<PERSON> yö<PERSON>in", "language": {"label": "Arayüz <PERSON>", "description": "Uygulama arayüzü için tercih ettiğiniz dili seçin."}, "timezone": {"label": "Saat Dilimi", "description": "Zaman diliminizi seçin", "placeholder": "Zaman dilimi seçin...", "searchPlaceholder": "Zaman dilimi ara...", "loading": "Zaman dilimleri yükleniyor...", "successMessage": "Saat dilimi başarıyla güncellendi.", "errorMessage": "Zaman dilimi güncellenemedi. Lütfen tekrar deneyin."}}, "notifications": {"title": "E-posta Bildirimleri", "description": "E-posta bildirim tercihlerinizi yönetin", "transcriptionSuccess": {"label": "Transkripsiyon Başarı Bildirimleri", "description": "Ses transkripsiyonunuz tamamlandığında bildirim alın"}, "quotaReset": {"label": "Transkripsiyon Kota Sıfırlama Bildirimleri", "description": "Transkripsiyon kotanız sıfırlandığında bildirim alın"}, "productUpdates": {"label": "<PERSON><PERSON><PERSON><PERSON> ve Yeni Özellikler", "description": "<PERSON><PERSON><PERSON><PERSON> güncellemeleri ve yeni özellikler hakkında bildirimler alın"}, "successMessage": "<PERSON><PERSON><PERSON><PERSON>ü<PERSON>di", "errorMessage": "Bildirim <PERSON>larını güncelleme başarısız oldu. Lütfen tekrar deneyin."}, "dangerZone": {"title": "Tehlikeli Bölge", "description": "<PERSON><PERSON>", "deleteAccount": {"label": "Hesabı Sil", "description": "Hesabınızı ve tüm verilerinizi kalıcı olarak silin.", "button": "Hesabı Sil", "dialog": {"title": "Hesabı Sil", "description": "Hesabınızı silmek istediğinizden emin misiniz?", "warning": "Bu işlem geri alınamaz.", "consequences": {"title": "Hesabınızı sildiğinizde:", "items": ["Tüm verileriniz kalıcı olarak silinecektir.", "Tüm transkripsiyonlarınıza erişiminizi kaybedeceksiniz.", "Aboneliğiniz iptal edilecektir (varsa)", "Hesabınız kurtarılamaz.", "E-posta adresiniz artık yeni bir hesap oluşturamayacaktır."]}, "confirmation": {"label": "Onaylamak için, aşağıya DELETE yazın:", "placeholder": "BURAYA DELETE YAZIN"}, "buttons": {"cancel": "İptal et", "delete": "Sil", "deleting": "Siliniyor..."}, "successMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "errorMessage": "Hesabı silme işlemi başarısız oldu. Lütfen tekrar deneyin."}}}, "apiKeyManagement": {"title": "API Anahtar Y<PERSON>imi", "description": "UniScribe API'ye erişim için API anahtarlarınızı yönetin.", "requiresSubscription": "API erişimi, aktif bir abonelik veya LTD planı gerektirir.", "upgradePrompt": "API yönetim özelliklerine erişmek için planınızı yükseltin.", "createKey": "<PERSON><PERSON>", "createFirstKey": "İlk API Anahtarınızı Oluşturun", "noKeys": "API Anahtarları Yok", "noKeysDescription": "Hen<PERSON>z herhangi bir API anahtarı oluşturmadınız.", "maxKeysReached": "Maksimum API anahtarları sınırına ulaşıldı (5 anahtar)", "maxKeysDescription": "Yeni bir tane oluşturmadan önce mevcut bir API anahtarını silin.", "keyName": "<PERSON><PERSON><PERSON>", "keyNamePlaceholder": "API anahtarınız için bir isim girin", "expiration": "<PERSON> kullanma tarihi", "noExpiration": "Son kullanma tarihi yok", "days": "<PERSON><PERSON><PERSON><PERSON>", "createdAt": "<PERSON><PERSON>ş<PERSON><PERSON><PERSON>", "lastUsed": "<PERSON>", "neverUsed": "<PERSON>ç kullanılmadı", "actions": "<PERSON><PERSON><PERSON>", "rename": "<PERSON><PERSON><PERSON>", "reset": "Sıfırla", "delete": "Sil", "copy": "Kopyala", "copied": "Kopyalandı!", "active": "Aktif", "expired": "S<PERSON><PERSON>i <PERSON>ş", "keyPreview": "<PERSON><PERSON><PERSON>", "fullKey": "Tam API Anahtarı", "keyWarning": "B<PERSON>, tam API anahtarını göreceğiniz tek zamandır. Bunu güvenli bir şekilde sa<PERSON>yın.", "keyStorageWarning": "<PERSON><PERSON> anah<PERSON><PERSON> ka<PERSON>, yeni bir tane almak için sıfırlamanız gerekecek.", "confirmDelete": "Bu API anahtarını silmek istediğinizden emin misiniz?", "confirmDeleteDescription": "Bu işlem geri alınamaz. Bu anahtarı kullanan tüm uygulamalar hemen çalışmayı durduracaktır.", "confirmReset": "Bu API anahtarını sıfırlamak istediğinizden emin misiniz?", "confirmResetDescription": "<PERSON><PERSON>, yeni bir anahtar değeri oluşturacaktır. Eski anahtar hemen devre dışı kalacaktır.", "createDialog": {"title": "API Anahtarı Oluşturun", "nameLabel": "<PERSON><PERSON><PERSON>", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON>, Üretim API Anahtarı", "expirationLabel": "Sonlandırma (isteğe bağlı)", "expirationPlaceholder": "<PERSON><PERSON><PERSON>", "expirationHelp": "<PERSON> kullanma tarihi o<PERSON><PERSON> i<PERSON><PERSON> bo<PERSON> bırakın (1-3650 gün)", "cancel": "İptal et", "create": "<PERSON><PERSON><PERSON>", "creating": "Oluşturuluyor..."}, "renameDialog": {"title": "API Anahtarını Yeniden Adlandırın", "nameLabel": "<PERSON><PERSON>", "cancel": "İptal et", "save": "<PERSON><PERSON>", "saving": "Kay<PERSON>ili<PERSON>r...", "description": "API anahtarınızın adını değiştirin."}, "keyCreatedDialog": {"title": "API Anahtarı Başarıyla Oluşturuldu", "copyButton": "API Anahtarını Kopyala", "close": "Ka<PERSON><PERSON>"}, "keyResetDialog": {"title": "API Anahtarı Başarıyla Sıfırlandı", "copyButton": "Yeni API Anahtarını Kopyala", "close": "Ka<PERSON><PERSON>"}, "successMessages": {"keyCreated": "API anahtarı başarıyla oluşturuldu.", "keyUpdated": "API anahtarı başarıyla güncellendi.", "keyReset": "API anahtarınız başarıyla sıfırlandı.", "keyDeleted": "API anahtarı başarıyla silindi."}, "errorMessages": {"loadFailed": "API anahtarları yüklenemedi. Lütfen tekrar deneyin.", "createFailed": "API anahtarını oluşturma başarısız oldu. Lütfen tekrar deneyin.", "updateFailed": "API anahtarını güncellemeyi başaramadınız. Lütfen tekrar deneyin.", "resetFailed": "API anahtarını sıfırlama işlemi başarısız oldu. Lütfen tekrar deneyin.", "deleteFailed": "API anahtarını silme işlemi başarısız oldu. Lütfen tekrar deneyin.", "accessDenied": "API erişimi, aktif bir abonelik veya LTD planı gerektirir.", "maxKeysReached": "Maksimum API anahtarı sayısına ulaşıldı (5)", "invalidName": "API anahtar adı 1 ile 100 karakter arasında olmalıdır.", "invalidExpiration": "Son kullanma tarihi 1 ile 3650 gün arasında olmalıdır.", "keyNotFound": "API anahtarı bulunamadı", "nameExists": "API anahtar adı zaten mevcut."}, "upgradePlan": "Yükseltme Planı", "viewApiDocs": "API Dokümantasyonunu Görüntüle"}, "usage": {"title": "Kullanım", "description": "Hesap kullanımınızı ve kalan kredilerinizi görü<PERSON><PERSON><PERSON>in."}}