{"page": {"title": "Impostazioni", "subtitle": "Gestisci le impostazioni e le preferenze del tuo account", "backButton": "Indietro"}, "navigation": {"profile": "<PERSON>ilo", "preferences": "Prefer<PERSON><PERSON>", "notifications": "Notifiche", "dangerZone": "Zona di Pericolo", "apiKeyManagement": "Chiavi API", "usage": "<PERSON><PERSON><PERSON><PERSON>"}, "profile": {"title": "<PERSON>ilo", "description": "Gestisci le tue informazioni personali", "firstName": "Nome di battesimo", "lastName": "Cognome", "emailAddress": "<PERSON><PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>", "saving": "Salvataggio...", "saved": "Salvato!", "successMessage": "<PERSON>ilo salvato con <PERSON>o", "errorMessage": "Impossibile salvare il profilo. Si prega di riprovare."}, "preferences": {"title": "Prefer<PERSON><PERSON>", "description": "Gest<PERSON>ci le preferenze della tua applicazione", "language": {"label": "Lingua dell'interfaccia", "description": "Scegli la tua lingua preferita per l'interfaccia dell'applicazione"}, "timezone": {"label": "<PERSON><PERSON>", "description": "Seleziona il tuo fuso orario", "placeholder": "Seleziona fuso orario...", "searchPlaceholder": "Cerca fuso orario...", "loading": "Caricamento dei fusi orari...", "successMessage": "Fuso orario aggiornato con successo", "errorMessage": "Impossibile aggiornare il fuso orario. Si prega di riprovare."}}, "notifications": {"title": "Notifiche Email", "description": "Gestisci le tue preferenze di notifica via email", "transcriptionSuccess": {"label": "Notifiche di Successo della Trascrizione", "description": "Ricevi notifiche quando la tua trascrizione audio è completa"}, "quotaReset": {"label": "Notifiche di Ripristino della Quota di Trascrizione", "description": "Ricevi notifiche quando la tua quota di trascrizione viene ripristinata"}, "productUpdates": {"label": "Aggiornamenti del prodotto e nuove funzionalità", "description": "Ricevi notifiche sugli aggiornamenti del prodotto e sulle nuove funzionalità"}, "successMessage": "Impostazioni di notifica aggiornate", "errorMessage": "Impossibile aggiornare le impostazioni di notifica. Si prega di riprovare."}, "dangerZone": {"title": "Zona di Pericolo", "description": "Azioni che non possono essere annullate", "deleteAccount": {"label": "Elimina Account", "description": "Elimina permanentemente il tuo account e tutti i dati", "button": "Elimina Account", "dialog": {"title": "Elimina Account", "description": "Sei sicuro di voler eliminare il tuo account?", "warning": "Questa azione non può essere annullata.", "consequences": {"title": "Quando elimini il tuo account:", "items": ["Tutti i tuoi dati verranno eliminati permanentemente.", "Perderai l'accesso a tutte le tue trascrizioni.", "La tua sottoscrizione sarà annullata (se applicabile)", "Il tuo account non può essere recuperato.", "La tua email non sarà più in grado di creare un nuovo account."]}, "confirmation": {"label": "Per confermare, digita DELETE qui sotto:", "placeholder": "Digita DELETE qui"}, "buttons": {"cancel": "<PERSON><PERSON><PERSON>", "delete": "Elimina", "deleting": "Eliminazione..."}, "successMessage": "Il tuo account è stato eliminato.", "errorMessage": "Impossibile eliminare l'account. Si prega di riprovare."}}}, "apiKeyManagement": {"title": "Gestione della chiave API", "description": "Gestisci le tue chiavi API per accedere all'API di UniScribe", "requiresSubscription": "L'accesso all'API richiede un abbonamento attivo o un piano LTD.", "upgradePrompt": "Aggiorna il tuo piano per accedere alle funzionalità di gestione API", "createKey": "<PERSON><PERSON>", "createFirstKey": "Crea la tua prima chiave API", "noKeys": "Nessuna chiave API", "noKeysDescription": "Non hai ancora creato alcuna chiave API.", "maxKeysReached": "Limite massimo di chiavi API raggiunto (5 chiavi)", "maxKeysDescription": "Elimina una chiave API esistente prima di crearne una nuova.", "keyName": "Nome della chiave", "keyNamePlaceholder": "Inserisci un nome per la tua chiave API", "expiration": "Scadenza", "noExpiration": "Nessuna scadenza", "days": "<PERSON>ior<PERSON>", "createdAt": "<PERSON><PERSON><PERSON>", "lastUsed": "<PERSON><PERSON><PERSON>", "neverUsed": "<PERSON> util<PERSON>", "actions": "Azioni", "rename": "Rinomina", "reset": "R<PERSON><PERSON><PERSON>", "delete": "Elimina", "copy": "Copia", "copied": "Copiato!", "active": "Attivo", "expired": "Scaduto", "keyPreview": "Anteprima Chiave", "fullKey": "Chiave API completa", "keyWarning": "Questo è l'unico momento in cui vedrai la chiave API completa. Conservala in modo sicuro.", "keyStorageWarning": "Se perdi questa chiave, dovrai reimpostarla per ottenerne una nuova.", "confirmDelete": "Sei sicuro di voler eliminare questa chiave API?", "confirmDeleteDescription": "Questa azione non può essere annullata. Tutte le applicazioni che utilizzano questa chiave smetteranno di funzionare immediatamente.", "confirmReset": "Sei sicuro di voler reimpostare questa chiave API?", "confirmResetDescription": "Questo genererà un nuovo valore di chiave. La vecchia chiave smetterà di funzionare immediatamente.", "createDialog": {"title": "<PERSON>rea chiave <PERSON>", "nameLabel": "<PERSON><PERSON>", "namePlaceholder": "e.g., Chiave API di Produzione", "expirationLabel": "Scadenza (opzionale)", "expirationPlaceholder": "Numero di giorni", "expirationHelp": "<PERSON><PERSON> vuoto per nessuna scadenza (1-3650 giorni)", "cancel": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON>", "creating": "Creazione..."}, "renameDialog": {"title": "Rinomina la chiave API", "nameLabel": "Nuovo Nome", "cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>", "saving": "Salvataggio...", "description": "Cambia il nome della tua chiave API."}, "keyCreatedDialog": {"title": "Chiave API creata con successo", "copyButton": "Copia la chiave API", "close": "<PERSON><PERSON>"}, "keyResetDialog": {"title": "Reimpostazione della chiave API avvenuta con successo", "copyButton": "Copia Nuova Chiave API", "close": "<PERSON><PERSON>"}, "successMessages": {"keyCreated": "Chiave API creata con successo", "keyUpdated": "Chiave API aggiornata con successo", "keyReset": "Reimpostazione della chiave API avvenuta con successo", "keyDeleted": "Chiave API eliminata con successo"}, "errorMessages": {"loadFailed": "Impossibile caricare le chiavi API. Si prega di riprovare.", "createFailed": "Impossibile creare la chiave API. Si prega di riprovare.", "updateFailed": "Impossibile aggiornare la chiave API. Si prega di riprovare.", "resetFailed": "Impossibile reimpostare la chiave API. Si prega di riprovare.", "deleteFailed": "Impossibile eliminare la chiave API. Si prega di riprovare.", "accessDenied": "L'accesso all'API richiede un abbonamento attivo o un piano LTD.", "maxKeysReached": "Numero massimo di chiavi API raggiunto (5)", "invalidName": "Il nome della chiave API deve essere compreso tra 1 e 100 caratteri.", "invalidExpiration": "La scadenza deve essere compresa tra 1 e 3650 giorni.", "keyNotFound": "Chiave API non trovata", "nameExists": "Il nome della chiave API esiste già."}, "upgradePlan": "Piano di Aggiornamento", "viewApiDocs": "Visualizza la documentazione API"}, "usage": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Visualizza l'utilizzo del tuo account e i crediti rimanenti"}}