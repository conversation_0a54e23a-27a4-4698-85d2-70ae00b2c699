{"page": {"title": "Настройки", "subtitle": "Управляйте настройками и предпочтениями вашей учетной записи", "backButton": "Назад"}, "navigation": {"profile": "Профиль", "preferences": "Настройки", "notifications": "Уведомления", "dangerZone": "Опасная зона", "apiKeyManagement": "API ключи", "usage": "Использование"}, "profile": {"title": "Профиль", "description": "Управляйте вашей личной информацией", "firstName": "Имя", "lastName": "Фамилия", "emailAddress": "Адрес электронной почты", "save": "Сохранить", "saving": "Сохранение...", "saved": "Сохранено!", "successMessage": "Профиль успешно сохранен", "errorMessage": "Не удалось сохранить профиль. Пожалуйста, попробуйте снова."}, "preferences": {"title": "Настройки", "description": "Управляйте настройками вашего приложения", "language": {"label": "Язык интерфейса", "description": "Выберите предпочитаемый язык для интерфейса приложения"}, "timezone": {"label": "Часовой пояс", "description": "Выберите ваш часовой пояс", "placeholder": "Выберите часовой пояс...", "searchPlaceholder": "Поиск часового пояса...", "loading": "Загрузка часовых поясов...", "successMessage": "Часовой пояс успешно обновлен", "errorMessage": "Не удалось обновить часовой пояс. Пожалуйста, попробуйте снова."}}, "notifications": {"title": "Уведомления по электронной почте", "description": "Управляйте своими предпочтениями уведомлений по электронной почте", "transcriptionSuccess": {"label": "Уведомления об успешной транскрипции", "description": "Получайте уведомления, когда ваша аудиозапись будет завершена."}, "quotaReset": {"label": "Уведомления о сбросе квоты транскрипции", "description": "Получайте уведомления, когда ваша квота транскрипции будет сброшена"}, "productUpdates": {"label": "Обновления продукта и новые функции", "description": "Получайте уведомления о обновлениях продукта и новых функциях"}, "successMessage": "Настройки уведомлений обновлены", "errorMessage": "Не удалось обновить настройки уведомлений. Пожалуйста, попробуйте снова."}, "dangerZone": {"title": "Опасная зона", "description": "Действия, которые нельзя отменить", "deleteAccount": {"label": "Удалить аккаунт", "description": "Навсегда удалить вашу учетную запись и все данные", "button": "Удалить аккаунт", "dialog": {"title": "Удалить аккаунт", "description": "Вы уверены, что хотите удалить свою учетную запись?", "warning": "Это действие нельзя отменить.", "consequences": {"title": "Когда вы удаляете свою учетную запись:", "items": ["Все ваши данные будут навсегда удалены.", "Вы потеряете доступ ко всем вашим транскрипциям.", "Ваша подписка будет отменена (если применимо)", "Вашу учетную запись нельзя восстановить", "Ваш адрес электронной почты больше не сможет создать новую учетную запись."]}, "confirmation": {"label": "Чтобы подтвердить, введите DELETE ниже:", "placeholder": "Введите DELETE здесь"}, "buttons": {"cancel": "Отменить", "delete": "Удалить", "deleting": "Удаление..."}, "successMessage": "<PERSON>а<PERSON> аккаунт был удален.", "errorMessage": "Не удалось удалить аккаунт. Пожалуйста, попробуйте снова."}}}, "apiKeyManagement": {"title": "Управление API-ключами", "description": "Управляйте своими ключами API для доступа к UniScribe API", "requiresSubscription": "Доступ к API требует активной подписки или плана LTD.", "upgradePrompt": "Обновите свой тарифный план, чтобы получить доступ к функциям управления API.", "createKey": "Создать новый ключ", "createFirstKey": "Создайте свой первый API-ключ", "noKeys": "Нет ключей API", "noKeysDescription": "Вы еще не создали никаких ключей API.", "maxKeysReached": "Достигнут максимальный лимит ключей API (5 ключей)", "maxKeysDescription": "Удалите существующий API-ключ перед созданием нового.", "keyName": "Имя ключа", "keyNamePlaceholder": "Введите имя для вашего API ключа", "expiration": "Истечение срока действия", "noExpiration": "Без срока действия", "days": "дни", "createdAt": "Создано", "lastUsed": "Последнее использование", "neverUsed": "Никогда не использовалось", "actions": "Действия", "rename": "Переименовать", "reset": "Сбросить", "delete": "Удалить", "copy": "Копировать", "copied": "Скопировано!", "active": "Активный", "expired": "Истек срок действия", "keyPreview": "Предварительный просмотр ключа", "fullKey": "Полный API-ключ", "keyWarning": "Это единственный раз, когда вы увидите полный API ключ. Храните его в безопасности.", "keyStorageWarning": "Если вы потеряете этот ключ, вам потребуется сбросить его, чтобы получить новый.", "confirmDelete": "Вы уверены, что хотите удалить этот API ключ?", "confirmDeleteDescription": "Это действие нельзя отменить. Все приложения, использующие этот ключ, немедленно прекратят свою работу.", "confirmReset": "Вы уверены, что хотите сбросить этот API ключ?", "confirmResetDescription": "Это создаст новый ключ. Старый ключ перестанет работать немедленно.", "createDialog": {"title": "Создать API-ключ", "nameLabel": "Имя ключа", "namePlaceholder": "например, Production API Key", "expirationLabel": "Срок действия (необязательно)", "expirationPlaceholder": "Количество дней", "expirationHelp": "Оставьте пустым для отсутствия срока действия (1-3650 дней)", "cancel": "Отменить", "create": "Создать ключ", "creating": "Создание..."}, "renameDialog": {"title": "Переименовать API Key", "nameLabel": "Новое имя", "cancel": "Отменить", "save": "Сохранить", "saving": "Сохранение...", "description": "Измените имя вашего API ключа."}, "keyCreatedDialog": {"title": "Ключ API успешно создан", "copyButton": "Скопировать API-ключ", "close": "Закрыть"}, "keyResetDialog": {"title": "Ключ API успешно сброшен", "copyButton": "Скопировать новый API-ключ", "close": "Закрыть"}, "successMessages": {"keyCreated": "Ключ API успешно создан", "keyUpdated": "Ключ API успешно обновлён", "keyReset": "Ключ API успешно сброшен", "keyDeleted": "Ключ API успешно удален"}, "errorMessages": {"loadFailed": "Не удалось загрузить ключи API. Пожалуйста, попробуйте снова.", "createFailed": "Не удалось создать API-ключ. Пожалуйста, попробуйте снова.", "updateFailed": "Не удалось обновить ключ API. Пожалуйста, попробуйте снова.", "resetFailed": "Не удалось сбросить ключ API. Пожалуйста, попробуйте снова.", "deleteFailed": "Не удалось удалить ключ API. Пожалуйста, попробуйте снова.", "accessDenied": "Доступ к API требует активной подписки или плана LTD.", "maxKeysReached": "Достигнуто максимальное количество ключей API (5)", "invalidName": "Имя ключа API должно содержать от 1 до 100 символов.", "invalidExpiration": "Срок действия должен быть от 1 до 3650 дней", "keyNotFound": "Ключ API не найден", "nameExists": "Имя ключа API уже существует"}, "upgradePlan": "План обновления", "viewApiDocs": "Просмотреть документацию API"}, "usage": {"title": "Использование", "description": "Просмотрите использование вашего аккаунта и оставшиеся кредиты"}}