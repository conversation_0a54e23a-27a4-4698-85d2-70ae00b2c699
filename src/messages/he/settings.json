{"page": {"title": "הגדרות", "subtitle": "נהל את הגדרות והעדפות החשבון שלך", "backButton": "חזרה"}, "navigation": {"profile": "פרופיל", "preferences": "העדפות", "notifications": "התראות", "dangerZone": "אזו<PERSON>ה", "apiKeyManagement": "מפתחות API", "usage": "שימוש"}, "profile": {"title": "פרופיל", "description": "נהל את המידע האישי שלך", "firstName": "שם פרטי", "lastName": "שם משפחה", "emailAddress": "כתובת דוא\"ל", "save": "שמור", "saving": "שומר...", "saved": "נשמר!", "successMessage": "הפרופיל נשמר בהצלחה", "errorMessage": "שימור הפרו<PERSON><PERSON>ל נכשל. אנא נסה שוב."}, "preferences": {"title": "העדפות", "description": "נהל את העדפות היישום שלך", "language": {"label": "שפת ממשק", "description": "בחר את השפה המועדפת שלך עבור ממשק היישום"}, "timezone": {"label": "א<PERSON><PERSON><PERSON>", "description": "בחר את אזור הזמן שלך", "placeholder": "בחר אזור זמן...", "searchPlaceholder": "ח<PERSON><PERSON> אזור זמן...", "loading": "טוען אזורי זמן...", "successMessage": "הזמן באזור עוד<PERSON>ן בהצלחה", "errorMessage": "נכשל בעד<PERSON><PERSON>ן אזור הזמן. אנא נסה שוב."}}, "notifications": {"title": "התראות דוא\"ל", "description": "נהל את העדפות ההודעות בדוא\"ל שלך", "transcriptionSuccess": {"label": "הודעות הצלחה של תמלול", "description": "קבל התראות כאשר ההקלטה שלך הושלמה"}, "quotaReset": {"label": "התראות על איפוס מכסת ההקלטות", "description": "קבל התראות כאשר מכסת ההקלדה שלך מתאפסה"}, "productUpdates": {"label": "עדכוני מוצר ותכונות חדשות", "description": "קבל התראות על עדכוני מוצר ותכונות חדשות"}, "successMessage": "הגדרות ההודעות עודכנו", "errorMessage": "נכשל בעד<PERSON><PERSON><PERSON> הגדרות ההודעות. אנא נסה שוב."}, "dangerZone": {"title": "אזו<PERSON>ה", "description": "פעולות שאינן ניתנות לביטול", "deleteAccount": {"label": "<PERSON><PERSON><PERSON>", "description": "מחק לצמיתות את החשבון שלך ואת כל הנתונים", "button": "<PERSON><PERSON><PERSON>", "dialog": {"title": "<PERSON><PERSON><PERSON>", "description": "האם אתה בטוח שאתה רוצה למחוק את החשבון שלך?", "warning": "פעולה זו אינה ניתנת לביטול.", "consequences": {"title": "כאשר אתה מוחק את החשבון שלך:", "items": ["כל הנתונים שלך יימחקו לצמיתות", "אתה תאבד גישה לכל ההקלטות שלך", "המנוי שלך יבוטל (אם רלוונטי)", "החשבון שלך אינו ניתן לשחזור", "האימייל שלך לא יוכל יותר ליצור חשבון חדש"]}, "confirmation": {"label": "כדי לאשר, ה<PERSON><PERSON><PERSON> DELETE למטה:", "placeholder": "הקלד DELETE כאן"}, "buttons": {"cancel": "ביטול", "delete": "מחק", "deleting": "מוחק..."}, "successMessage": "החשבון שלך נמחק", "errorMessage": "נכשל במח<PERSON>ת החשבון. אנא נסה שוב."}}}, "apiKeyManagement": {"title": "ניהול מפתחות API", "description": "נהל את מפתחות ה-API שלך לגישה ל-UniScribe API", "requiresSubscription": "גישה ל-API דורשת מנוי פעיל או תוכנית LTD", "upgradePrompt": "שדרגו את התוכנית שלכם כדי לגשת לתכונות ניהול ה-API", "createKey": "צור מפתח חדש", "createFirstKey": "צור את מפתח ה-API הראשון שלך", "noKeys": "אין מפתחות API", "noKeysDescription": "עדיין לא יצרת מפתחות API.", "maxKeysReached": "הגעת למגבלת המפתחות של ה-API המקסימלית (5 מפתחות)", "maxKeysDescription": "מחק מפתח API קיים לפני יצירת חדש.", "keyName": "שם מפתח", "keyNamePlaceholder": "הזן שם עבור מפתח ה-API שלך", "expiration": "תוקף", "noExpiration": "אין תאריך תפוגה", "days": "ימים", "createdAt": "נוצר", "lastUsed": "שימו<PERSON> אחרון", "neverUsed": "מעולם לא השתמשתי", "actions": "פעולות", "rename": "שנה שם", "reset": "איפוס", "delete": "מחק", "copy": "העתק", "copied": "הועתק!", "active": "פעיל", "expired": "פג תוקף", "keyPreview": "תצוגה מקדימה של מפתח", "fullKey": "מפתח API מלא", "keyWarning": "זהו הזמן היחיד שבו תראה את מפתח ה-API המלא. שמור אותו בבטחה.", "keyStorageWarning": "אם תאבד את המפתח הזה, תצטרך לאפס אותו כדי לקבל חדש.", "confirmDelete": "האם אתה בטוח שברצונך למחוק את מפתח ה-API הזה?", "confirmDeleteDescription": "פעולה זו אינה ניתנת לביטול. כל היישומים המשתמשים במפתח זה יפסיקו לפעול מיד.", "confirmReset": "האם אתה בטוח שברצונך לאפס את מפתח ה-API הזה?", "confirmResetDescription": "זה ייצור ערך מפתח חדש. המפתח הישן יפסיק לפעול מיד.", "createDialog": {"title": "צור מפתח API", "nameLabel": "שם מפתח", "namePlaceholder": "מפתח <PERSON> לייצור", "expirationLabel": "תאריך תפוגה (אופציונלי)", "expirationPlaceholder": "מספר ימים", "expirationHelp": "השאר ריק ללא תאריך תפוגה (1-3650 ימים)", "cancel": "ביטול", "create": "צור מפתח", "creating": "יוצר..."}, "renameDialog": {"title": "שנה את מפתח ה-API", "nameLabel": "שם חדש", "cancel": "ביטול", "save": "שמור", "saving": "שומר...", "description": "שנה את שם מפתח ה-API שלך."}, "keyCreatedDialog": {"title": "מפתח API נוצר בהצלחה", "copyButton": "העתק מפתח API", "close": "סגור"}, "keyResetDialog": {"title": "איפוס מפתח ה-API בוצע בהצלחה", "copyButton": "העתק מפתח API חדש", "close": "סגור"}, "successMessages": {"keyCreated": "מפתח ה-API נוצר בהצלחה", "keyUpdated": "מפתח ה-API עודכן בהצלחה", "keyReset": "מפתח ה-API שוחזר בהצלחה", "keyDeleted": "מפתח ה-API נמחק בהצלחה"}, "errorMessages": {"loadFailed": "לא הצלחנו לטעון את מפתחות ה-API. אנא נסה שוב.", "createFailed": "נכשל ביצירת מפתח API. אנא נסה שוב.", "updateFailed": "עדכון מפתח ה-API נכשל. אנא נסה שוב.", "resetFailed": "נכשל בהגדרת מפתח ה-API. אנא נסה שוב.", "deleteFailed": "נכשל במחקת מפתח ה-API. אנא נסה שוב.", "accessDenied": "גישה ל-API דורשת מנוי פעיל או תוכנית LTD", "maxKeysReached": "המספר המקסימלי של מפתחות API הושג (5)", "invalidName": "שם מפתח ה-API חייב להיות בין 1 ל-100 תווים", "invalidExpiration": "תוקף חייב להיות בין 1 ל-3650 ימים", "keyNotFound": "מפתח API לא נמצא", "nameExists": "שם מפתח ה-API כבר קיים"}, "upgradePlan": "תוכנית שדרוג", "viewApiDocs": "צ<PERSON>ה בתיעוד ה-API"}, "usage": {"title": "שימוש", "description": "צפה בשימוש בחשבון שלך ובזיכויים שנותרו"}}